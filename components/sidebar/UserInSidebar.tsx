'use client';

import { useTransition, useState, useEffect, useMemo } from 'react';
import Image from 'next/image';
import {
	Dropdown,
	DropdownTrigger,
	DropdownMenu,
	DropdownItem,
	DropdownSection,
} from '@heroui/dropdown';
import { Spinner } from '@heroui/spinner';
import {
	IconChevronDown,
	IconChevronUp,
	IconLogout,
	IconCrown,
	IconSettings,
	IconUser,
} from '@tabler/icons-react';
import { useSidebarVisibility, useUserContext } from '@/app/(post-auth)/providers';
import { signOutUser } from '@/app/login/actions/SignOutUser';
import { generateCustomerPortalUrl } from '@/app/subscriptions/actions';
import { addToast } from '@heroui/toast';

export const UserInSidebar = () => {
	const user = useUserContext();
	const isSidebarOpen = useSidebarVisibility();
	const [isPending, startTransition] = useTransition();
	const [isDropdownOpen, setIsDropdownOpen] = useState(false);
	const [isPortalLoading, setIsPortalLoading] = useState(false);
	const isPro = user?.user_metadata?.is_pro_user || false;

	// Close dropdown when sidebar closes (only for desktop)
	useEffect(() => {
		// On desktop, close dropdown when sidebar closes
		// On mobile, this component is unmounted when mobile sidebar closes
		if (
			typeof window !== 'undefined' &&
			window.innerWidth >= 768 &&
			!isSidebarOpen &&
			isDropdownOpen
		) {
			setIsDropdownOpen(false);
		}
	}, [isSidebarOpen, isDropdownOpen]);

	const handleManageSubscription = async () => {
		if (!isPro) return;

		setIsPortalLoading(true);
		try {
			const result = await generateCustomerPortalUrl();

			if (result.success) {
				// Open customer portal in new window
				window.open(result.portalUrl, '_blank', 'noopener,noreferrer');
				// Close the dropdown after successful portal opening
				setIsDropdownOpen(false);
			} else {
				addToast({
					title: 'Error',
					description: result.error.message || 'Failed to open customer portal',
					color: 'danger',
				});
			}
		} catch (error) {
			addToast({
				title: 'Error',
				description: 'An unexpected error occurred while opening the customer portal',
				color: 'danger',
			});
		} finally {
			setIsPortalLoading(false);
		}
	};

	const memoizedUser = useMemo(
		() => (
			<div className="flex items-center gap-2 overflow-hidden">
				<div className="relative h-8 w-8">
					{user.user_metadata.avatar_url ? (
						<Image
							src={user.user_metadata.avatar_url}
							alt={`${user.user_metadata.full_name}'s avatar`}
							width={32}
							height={32}
							sizes="32px"
							className="rounded-full object-cover"
							priority={true}
						/>
					) : (
						<div className="flex h-8 w-8 items-center justify-center rounded-full bg-foreground-100">
							<IconUser
								size={16}
								className="text-foreground-600"
							/>
						</div>
					)}
				</div>
				<div className="min-w-0 flex-1">
					<p className="truncate text-sm">{user.user_metadata.full_name}</p>
					<p className="truncate text-xs text-foreground-500">{user.email}</p>
				</div>
			</div>
		),
		[user]
	);

	return (
		<Dropdown
			isOpen={isDropdownOpen}
			onOpenChange={setIsDropdownOpen}
			placement="top"
		>
			<DropdownTrigger>
				<div className="mt-5 flex w-full cursor-pointer items-center justify-between px-2 py-1.5">
					{memoizedUser}
					<div className="flex h-6 w-6 flex-col items-center justify-center -space-y-1.5">
						<IconChevronUp className="h-3.5 w-3.5" />
						<IconChevronDown className="h-3.5 w-3.5" />
					</div>
				</div>
			</DropdownTrigger>
			<DropdownMenu
				aria-label="User menu"
				className="text-sm"
				disabledKeys={isPending ? ['logout'] : []}
				itemClasses={{
					base: 'text-sm',
					description: 'text-xs text-foreground-500',
					title: 'text-xs',
				}}
			>
				{isPortalLoading ? (
					<DropdownSection>
						<DropdownItem
							key="loading"
							textValue="loading customer portal"
							className="cursor-default hover:bg-transparent!"
							startContent={
								<Spinner
									size="sm"
									color="primary"
									className="mr-0"
								/>
							}
						>
							Opening Customer Portal...
						</DropdownItem>
					</DropdownSection>
				) : (
					<>
						<DropdownSection showDivider>
							<DropdownItem
								key="header"
								textValue="user profile"
								className="cursor-default hover:bg-transparent!"
							>
								{memoizedUser}
							</DropdownItem>
						</DropdownSection>
						<DropdownItem
							key="plan"
							textValue="user subscription plan"
							className="cursor-default hover:bg-transparent!"
							startContent={
								<IconCrown
									className="text-amber-500"
									size={18}
								/>
							}
							endContent={
								<span
									className={`rounded-full px-2 py-0.5 text-xs ${
										isPro
											? 'bg-amber-500/10 text-amber-600'
											: 'bg-primary/10 text-primary'
									}`}
								>
									{isPro ? 'Pro' : 'Free'}
								</span>
							}
						>
							My Plan
						</DropdownItem>

						{isPro ? (
							<DropdownItem
								key="manage"
								textValue="manage subscription"
								startContent={
									<IconSettings
										size={18}
										className="text-primary"
									/>
								}
								onPress={handleManageSubscription}
							>
								Manage Subscription
							</DropdownItem>
						) : null}

						<DropdownItem
							key="logout"
							textValue="logout user"
							startContent={
								isPending ? (
									<Spinner
										size="sm"
										color="danger"
										className="mr-0"
									/>
								) : (
									<IconLogout
										size={18}
										className="text-danger"
									/>
								)
							}
							onPress={() => startTransition(() => signOutUser())}
							closeOnSelect={false}
						>
							<span className="text-danger">
								{isPending ? 'Logging out...' : 'Log out'}
							</span>
						</DropdownItem>
					</>
				)}
			</DropdownMenu>
		</Dropdown>
	);
};
